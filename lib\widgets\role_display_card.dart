import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/widgets/ai_avatar.dart';

/// 角色展示卡片的显示模式
enum RoleDisplayMode {
  /// 网格模式 - 用于角色列表
  grid,
  /// 横幅模式 - 用于gacha页面等大图展示
  banner,
}

/// 通用角色展示卡片组件
/// 
/// 支持多种显示模式，统一角色卡片的样式和交互
class RoleDisplayCard extends StatefulWidget {
  /// 角色数据
  final AiRole role;
  
  /// 点击回调
  final VoidCallback onTap;
  
  /// 显示模式
  final RoleDisplayMode mode;
  
  /// 自定义高度（仅banner模式有效）
  final double? height;
  
  /// 自定义宽度
  final double? width;
  
  /// 是否启用动画效果
  final bool enableAnimation;
  
  const RoleDisplayCard({
    Key? key,
    required this.role,
    required this.onTap,
    this.mode = RoleDisplayMode.grid,
    this.height,
    this.width,
    this.enableAnimation = true,
  }) : super(key: key);
  
  @override
  State<RoleDisplayCard> createState() => _RoleDisplayCardState();
}

class _RoleDisplayCardState extends State<RoleDisplayCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    if (widget.enableAnimation) {
      _scaleController = AnimationController(
        duration: const Duration(milliseconds: 100),
        vsync: this,
      );
      _scaleAnimation = Tween<double>(
        begin: 1.0,
        end: 0.95,
      ).animate(CurvedAnimation(
        parent: _scaleController,
        curve: Curves.easeInOut,
      ));
    }
  }

  @override
  void dispose() {
    if (widget.enableAnimation) {
      _scaleController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget card = widget.mode == RoleDisplayMode.banner
        ? _buildBannerCard()
        : _buildGridCard();
        
    if (!widget.enableAnimation) {
      return card;
    }
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: card,
        );
      },
    );
  }

  /// 构建横幅模式卡片
  Widget _buildBannerCard() {
    return Container(
      height: widget.height ?? 200,
      width: widget.width,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: _buildCardContent(),
    );
  }

  /// 构建网格模式卡片
  Widget _buildGridCard() {
    return Container(
      width: widget.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(StringsConsts.recommendCardBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildCardContent(),
    );
  }

  /// 构建卡片内容
  Widget _buildCardContent() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        splashColor: Colors.white.withOpacity(0.1),
        highlightColor: Colors.transparent,
        borderRadius: BorderRadius.circular(
          widget.mode == RoleDisplayMode.banner ? 16 : StringsConsts.recommendCardBorderRadius
        ),
        onTap: _handleTap,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(
            widget.mode == RoleDisplayMode.banner ? 16 : StringsConsts.recommendCardBorderRadius
          ),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // 背景图片
              _buildCoverImage(),
              
              // 渐变遮罩
              _buildGradientOverlay(),
              
              // 角色信息
              _buildRoleInfo(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建封面图片
  Widget _buildCoverImage() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
      ),
      child: CachedNetworkImage(
        imageUrl: widget.role.coverUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Colors.grey[900],
          child: const Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Colors.white,
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey[800],
          child: const Icon(Icons.smart_toy_outlined, color: Colors.white70),
        ),
        fadeInDuration: const Duration(milliseconds: 200),
      ),
    );
  }

  /// 构建渐变遮罩
  Widget _buildGradientOverlay() {
    return Positioned.fill(
      child: DecoratedBox(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.2),
              Colors.black.withValues(alpha: 0.5),
              Colors.black.withValues(alpha: 0.8),
            ],
            stops: const [0.5, 0.7, 0.8, 1.0],
          ),
        ),
      ),
    );
  }

  /// 构建角色信息
  Widget _buildRoleInfo() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: widget.mode == RoleDisplayMode.banner ? 16 : 12,
          vertical: widget.mode == RoleDisplayMode.banner ? 16 : 12,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头像和名字行
            Row(
              children: [
                // 头像
                Hero(
                  tag: 'role_avatar_${widget.role.id}',
                  child: AiAvatar(
                    characterId: widget.role.id,
                    avatarUrl: widget.role.avatarUrl,
                    size: widget.mode == RoleDisplayMode.banner ? 40.0 : 32.0,
                    borderColor: Colors.white,
                    borderWidth: 2.0,
                  ),
                ),
                const SizedBox(width: 8),
                // 名字
                Expanded(
                  child: Text(
                    widget.role.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: widget.mode == RoleDisplayMode.banner ? 24 : 18,
                      color: Colors.white,
                      shadows: const [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 3.0,
                          color: Color.fromARGB(150, 0, 0, 0),
                        ),
                      ],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            
            // 描述文字（仅banner模式显示）
            if (widget.mode == RoleDisplayMode.banner) ...[
              const SizedBox(height: 4),
              Text(
                'Collect ${widget.role.name} cards!',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  shadows: [
                    Shadow(
                      color: Colors.black,
                      offset: Offset(0, 1),
                      blurRadius: 2.0,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 处理点击事件
  void _handleTap() {
    if (widget.enableAnimation) {
      if (!_isPressed) {
        _isPressed = true;
        _scaleController.forward().then((_) {
          _scaleController.reverse().then((_) {
            _isPressed = false;
            widget.onTap();
          });
        });
      }
    } else {
      widget.onTap();
    }
  }
}
