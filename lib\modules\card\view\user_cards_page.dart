import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rolio/routes/router_manager.dart';
import '../controller/card_controller.dart';
import '../model/card.dart' as card_model;
import '../model/card_rarity.dart';
import '../widget/card_item_widget.dart';

/// User cards page
class UserCardsPage extends StatelessWidget {
  const UserCardsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CardController>();
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.grey[900],
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () => RouterManager.goBack(),
        ),
        title: const Text(
          'My Cards',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(
              Icons.refresh,
              color: Colors.white,
            ),
            onPressed: () => controller.refreshUserCards(),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            color: Colors.grey[800],
            onSelected: (value) {
              switch (value) {
                case 'test_data':
                  controller.createTestData();
                  break;
                case 'clear':
                  controller.clearUserCards();
                  break;
                case 'sort_rarity':
                  controller.setSortOrder('rarity');
                  break;
                case 'sort_name':
                  controller.setSortOrder('name');
                  break;
                case 'sort_date':
                  controller.setSortOrder('date');
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'sort_rarity',
                child: Text('Sort by Rarity', style: TextStyle(color: Colors.white)),
              ),
              const PopupMenuItem(
                value: 'sort_name',
                child: Text('Sort by Name', style: TextStyle(color: Colors.white)),
              ),
              const PopupMenuItem(
                value: 'sort_date',
                child: Text('Sort by Date', style: TextStyle(color: Colors.white)),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'test_data',
                child: Text('Create Test Data', style: TextStyle(color: Colors.green)),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: Text('Clear Collection', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Stats section
          _buildStatsSection(controller),
          
          // Filter section
          _buildFilterSection(controller),
          
          // Search section
          _buildSearchSection(controller),
          
          // Cards grid
          Expanded(
            child: Obx(() {
              if (controller.isLoading) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.blue),
                );
              }
              
              final cards = controller.filteredUserCards;
              
              if (cards.isEmpty) {
                return _buildEmptyState();
              }
              
              return RefreshIndicator(
                onRefresh: () => controller.refreshUserCards(),
                child: GridView.builder(
                  padding: const EdgeInsets.all(24),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.65,
                    crossAxisSpacing: 28,
                    mainAxisSpacing: 28,
                  ),
                  itemCount: cards.length,
                  itemBuilder: (context, index) {
                    return CardItemWidget(
                      card: cards[index],
                      onTap: () => _showCardDetails(cards[index]),
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  /// Build stats section
  Widget _buildStatsSection(CardController controller) {
    return Obx(() => Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          // First row: Total and Unique
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('Total', controller.totalCardCount.toString()),
              _buildStatItem('Unique', controller.uniqueCardCount.toString()),
            ],
          ),
          const SizedBox(height: 12),
          // Second row: All rarities
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('Common', controller.getCardCountByRarity(CardRarity.common).toString()),
              _buildStatItem('Rare', controller.getCardCountByRarity(CardRarity.rare).toString()),
              _buildStatItem('Epic', controller.getCardCountByRarity(CardRarity.epic).toString()),
              _buildStatItem('Legendary', controller.getCardCountByRarity(CardRarity.legendary).toString()),
              _buildStatItem('Mythic', controller.getCardCountByRarity(CardRarity.mythic).toString()),
            ],
          ),
        ],
      ),
    ));
  }

  /// Build stat item
  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// Build filter section
  Widget _buildFilterSection(CardController controller) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Obx(() => ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip(
            controller,
            'All',
            controller.selectedRarity.value == null,
            () => controller.setRarityFilter(null),
          ),
          ...CardRarity.values.map((rarity) => _buildFilterChip(
            controller,
            rarity.displayName,
            controller.selectedRarity.value == rarity,
            () => controller.setRarityFilter(rarity),
            color: Color(rarity.colorValue),
          )),
        ],
      )),
    );
  }

  /// Build filter chip
  Widget _buildFilterChip(
    CardController controller,
    String label,
    bool isSelected,
    VoidCallback onTap, {
    Color? color,
  }) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (_) => onTap(),
        backgroundColor: Colors.grey[800],
        selectedColor: color ?? Colors.blue,
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[300],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  /// Build search section
  Widget _buildSearchSection(CardController controller) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TextField(
        onChanged: controller.setSearchQuery,
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: 'Search cards...',
          hintStyle: const TextStyle(color: Colors.grey),
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          filled: true,
          fillColor: Colors.grey[900],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
        ),
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.style_outlined,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 20),
          const Text(
            'No cards found',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Try adjusting your filters or pull some cards!',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 30),
          ElevatedButton(
            onPressed: () => Get.snackbar(
              'Info',
              'Go to a character detail page to draw cards for that character',
              backgroundColor: Colors.blue,
              colorText: Colors.white,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('How to Draw Cards'),
          ),
        ],
      ),
    );
  }

  /// Show card details
  void _showCardDetails(card_model.Card card) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 300, maxHeight: 500),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Color(card.rarity.colorValue), width: 2),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Card image
              ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(14)),
                child: CachedNetworkImage(
                  imageUrl: card.imageUrl,
                  height: 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    height: 200,
                    color: Colors.grey[800],
                    child: const Center(
                      child: CircularProgressIndicator(color: Colors.blue),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    height: 200,
                    color: Colors.grey[800],
                    child: const Icon(Icons.error, color: Colors.red),
                  ),
                ),
              ),
              
              // Card info
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      card.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Color(card.rarity.colorValue),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            card.rarity.displayName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Owned: ${card.ownedCount}',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      card.description,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Close button
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('Close'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
