import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controller/card_controller.dart';
import '../widget/gacha_animation_widget.dart';
import '../widget/card_item_widget.dart';
import '../model/card.dart' as card_model;
import '../model/card_rarity.dart';
import '../../wallet/controller/wallet_controller.dart';
import '../../../common/models/ai_role.dart';

/// Role-specific gacha page
class RoleGachaPage extends GetView<CardController> {
  const RoleGachaPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AiRole role = Get.arguments as AiRole;
    final walletController = Get.find<WalletController>();

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Background with main character
          _buildBackgroundImage(role),

          // Top navigation bar
          _buildTopNavigation(walletController),

          // Theme label (top left)
          _buildThemeLabel(),

          // Market button (top right)
          _buildMarketButton(),

          // Character name at top center
          _buildTopCharacterName(role),

          // Probability button at top right of gacha container
          _buildProbabilityButton(),

          // Bottom content area
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomContent(role),
          ),
        ],
      ),
    );
  }


  /// Build background image with main character
  Widget _buildBackgroundImage(AiRole role) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: NetworkImage(role.coverUrl.isNotEmpty ? role.coverUrl : role.avatarUrl),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withValues(alpha: 0.3),
                Colors.black.withValues(alpha: 0.8),
              ],
              stops: const [0.0, 1.0],
            ),
          ),
        ),
      ),
    );
  }

  /// Build top navigation bar
  Widget _buildTopNavigation(WalletController walletController) {
    return Positioned(
      top: 50,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back button
            Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(25),
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white, size: 24),
                onPressed: () => Get.back(),
              ),
            ),

            // Balance display
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: Colors.yellow.withValues(alpha: 0.8), width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.yellow.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.diamond, color: Colors.yellow, size: 18),
                  const SizedBox(width: 6),
                  Obx(() => Text(
                    '${walletController.currentBalance}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build theme label (top left)
  Widget _buildThemeLabel() {
    return Positioned(
      top: 120,
      left: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.red.withValues(alpha: 0.9),
              Colors.red[700]!.withValues(alpha: 0.9),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red, width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.red.withValues(alpha: 0.4),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Colors.white,
                  size: 18,
                ),
                SizedBox(width: 6),
                Text(
                  'Theme',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 2),
            Text(
              'Special Card',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build market button (top right)
  Widget _buildMarketButton() {
    return Positioned(
      top: 120,
      right: 16,
      child: GestureDetector(
        onTap: () {
          // TODO: Navigate to market page
          Get.snackbar(
            'Market',
            'Market feature coming soon!',
            backgroundColor: Colors.yellow[700],
            colorText: Colors.black,
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.yellow[600]!,
                Colors.yellow[700]!,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.yellow[800]!, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.yellow.withValues(alpha: 0.4),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.store, color: Colors.black, size: 18),
              SizedBox(width: 6),
              Text(
                'Market',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build top character name with probability button
  Widget _buildTopCharacterName(AiRole role) {
    // Limit character name length to prevent blocking too much content
    String displayName = role.name.length > 8 ? '${role.name.substring(0, 8)}...' : role.name;

    return Positioned(
      top: 200,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            displayName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  offset: Offset(1, 1),
                  blurRadius: 3,
                  color: Colors.black,
                ),
                Shadow(
                  offset: Offset(-1, -1),
                  blurRadius: 3,
                  color: Colors.black,
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: _showRateInfo,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
              ),
              child: const Text(
                '概率',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build probability button
  Widget _buildProbabilityButton() {
    return Positioned(
      bottom: 320, // Position above the gacha container
      right: 20,
      child: GestureDetector(
        onTap: () {
          _showProbabilityDialog();
        },
        child: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.8),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
          ),
          child: const Icon(
            Icons.info_outline,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }

  /// Build bottom content area
  Widget _buildBottomContent(AiRole role) {
    return Container(
      height: 250, // Reduced height for more compact layout
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 20), // Top spacing

          // Cards horizontal scroll
          _buildCardsHorizontalScroll(role),

          const SizedBox(height: 20), // Spacing between cards and buttons

          // Gacha buttons row (only single and ten pull)
          _buildSimpleGachaButtonsRow(role),

          const SizedBox(height: 20), // Bottom spacing
        ],
      ),
    );
  }



  /// Build cards horizontal scroll
  Widget _buildCardsHorizontalScroll(AiRole role) {
    return SizedBox(
      height: 130, // Reduced height to accommodate smaller cards
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        clipBehavior: Clip.none, // Allow glow effects to show outside bounds
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 6, // Show 6 sample cards
        itemBuilder: (context, index) {
          final card = _createSampleCard(role, index);
          return Container(
            width: 65, // Reduced card width for better spacing
            margin: const EdgeInsets.only(right: 20), // Adjusted margin for smaller cards
            child: CardItemWidget(
              card: card,
              showOwnedCount: false,
              width: 65, // Reduced card width
              height: 95, // Reduced card height
            ),
          );
        },
      ),
    );
  }

  /// Create a sample card from AiRole
  card_model.Card _createSampleCard(AiRole role, [int? index]) {
    // Use different rarities for variety
    final rarities = [
      CardRarity.common,
      CardRarity.rare,
      CardRarity.epic,
      CardRarity.legendary,
      CardRarity.mythic,
    ];

    // Select rarity based on role ID and index for consistency
    final rarityIndex = (role.id + (index ?? 0)) % rarities.length;
    final rarity = rarities[rarityIndex];

    // Simulate owned status - some cards are owned, some are not
    final isOwned = (index ?? 0) % 3 == 0; // Every 3rd card is owned

    return card_model.Card(
      id: 'sample_${role.id}_${index ?? 0}',
      name: '${role.name} ${index != null ? 'Card ${index + 1}' : 'Card'}',
      description: role.description,
      imageUrl: role.coverUrl.isNotEmpty ? role.coverUrl : role.avatarUrl,
      rarity: rarity,
      roleId: role.id.toString(),
      roleName: role.name,
      createdAt: DateTime.now(),
      isOwned: isOwned,
      ownedCount: isOwned ? 1 : 0,
    );
  }



  /// Build simple gacha buttons row (only single and ten pull)
  Widget _buildSimpleGachaButtonsRow(AiRole role) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Single pull button
          Expanded(
            child: _buildGachaButtonNew(
              title: 'Gacha x1',
              cost: 180,
              color: Colors.grey[800]!,
              textColor: Colors.white,
              onTap: () => _performRoleGacha(role, 1),
            ),
          ),

          const SizedBox(width: 16),

          // Ten pull button
          Expanded(
            child: _buildGachaButtonNew(
              title: 'Gacha x10',
              cost: 1800,
              color: Colors.yellow[700]!,
              textColor: Colors.black,
              onTap: () => _performRoleGacha(role, 10),
            ),
          ),
        ],
      ),
    );
  }

  /// Build new style gacha button
  Widget _buildGachaButtonNew({
    required String title,
    required int cost,
    required Color color,
    required Color textColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 55, // Reduced height
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red, width: 2),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(
                color: textColor,
                fontSize: 15, // Slightly smaller font
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 1), // Reduced spacing
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Cost: $cost',
                  style: TextStyle(
                    color: textColor.withValues(alpha: 0.8),
                    fontSize: 11, // Smaller font
                  ),
                ),
                const SizedBox(width: 2),
                const Icon(
                  Icons.diamond,
                  color: Colors.blue,
                  size: 11, // Smaller icon
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Show rate information dialog
  void _showRateInfo() {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.5)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Drop Rates',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildRateItem('Mythic', '0.2%', Colors.red),
              _buildRateItem('Legendary', '2.8%', Colors.orange),
              _buildRateItem('Epic', '12%', Colors.purple),
              _buildRateItem('Rare', '25%', Colors.blue),
              _buildRateItem('Common', '60%', Colors.grey),
              const SizedBox(height: 16),
              Align(
                alignment: Alignment.center,
                child: TextButton(
                  onPressed: () => Get.back(),
                  child: const Text(
                    'Close',
                    style: TextStyle(
                      color: Colors.blue,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build rate item
  Widget _buildRateItem(String rarity, String rate, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            rarity,
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            rate,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Perform role-specific gacha
  void _performRoleGacha(AiRole role, int count) async {
    try {
      // For now, use the general gacha system
      // TODO: Implement role-specific gacha logic
      final result = count == 1
        ? await controller.performSinglePull()
        : await controller.performTenPull();

      // Show animation
      if (result != null) {
        Get.to(() => GachaAnimationWidget(result: result));
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        e.toString(),
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Show probability dialog
  void _showProbabilityDialog() {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'Gacha Rates',
          style: TextStyle(color: Colors.white),
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'SSR (5★): 1.5%',
              style: TextStyle(color: Colors.orange, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text(
              'SR (4★): 13%',
              style: TextStyle(color: Colors.purple, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text(
              'R (3★): 85.5%',
              style: TextStyle(color: Colors.blue, fontSize: 16),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              'Close',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
