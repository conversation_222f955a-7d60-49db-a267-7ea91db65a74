import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../model/gacha_result.dart';
import 'card_item_widget.dart';

/// Gacha animation widget
class GachaAnimationWidget extends StatefulWidget {
  final GachaResult result;

  const GachaAnimationWidget({
    super.key,
    required this.result,
  });

  @override
  State<GachaAnimationWidget> createState() => _GachaAnimationWidgetState();
}

class _GachaAnimationWidgetState extends State<GachaAnimationWidget>
    with TickerProviderStateMixin {
  late AnimationController _shakeController;
  late AnimationController _flipController;
  late AnimationController _bounceController;
  late AnimationController _revealController;

  late Animation<double> _shakeAnimation;
  late Animation<double> _flipAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _revealAnimation;

  bool _showCards = false;
  int _currentCardIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimation();
  }

  void _initializeAnimations() {
    // Shake animation (card back shaking)
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticInOut,
    ));

    // Flip animation (card turning)
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _flipAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: Curves.easeInOut,
    ));

    // Bounce animation (card bouncing)
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _bounceAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.bounceOut,
    ));

    // Reveal animation (cards appearing one by one)
    _revealController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _revealAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _revealController,
      curve: Curves.easeOut,
    ));
  }

  Future<void> _startAnimation() async {
    // Phase 1: Shake the card back
    await _shakeController.forward();
    
    // Phase 2: Flip the card
    await _flipController.forward();
    
    // Phase 3: Bounce effect
    await _bounceController.forward();
    
    // Phase 4: Show cards
    setState(() {
      _showCards = true;
    });
    
    // Phase 5: Reveal cards one by one
    if (widget.result.cards.length > 1) {
      for (int i = 0; i < widget.result.cards.length; i++) {
        setState(() {
          _currentCardIndex = i;
        });
        _revealController.reset();
        await _revealController.forward();
        await Future.delayed(const Duration(milliseconds: 200));
      }
    } else {
      _revealController.forward();
    }
  }

  @override
  void dispose() {
    _shakeController.dispose();
    _flipController.dispose();
    _bounceController.dispose();
    _revealController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Background effects
          _buildBackgroundEffects(),
          
          // Main content
          Center(
            child: _showCards ? _buildCardsReveal() : _buildCardAnimation(),
          ),
          
          // Skip button
          Positioned(
            top: 50,
            right: 20,
            child: TextButton(
              onPressed: _skipAnimation,
              child: const Text(
                'Skip',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
          
          // Continue button (after animation)
          if (_showCards && _currentCardIndex >= widget.result.cards.length - 1)
            Positioned(
              bottom: 50,
              left: 20,
              right: 20,
              child: ElevatedButton(
                onPressed: () => Get.back(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Continue'),
              ),
            ),
        ],
      ),
    );
  }

  /// Build background effects
  Widget _buildBackgroundEffects() {
    return AnimatedBuilder(
      animation: _flipAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.0 + _flipAnimation.value * 0.5,
              colors: [
                Colors.purple.withValues(alpha: 0.3 * _flipAnimation.value),
                Colors.blue.withValues(alpha: 0.2 * _flipAnimation.value),
                Colors.black,
              ],
            ),
          ),
          child: CustomPaint(
            painter: _ParticlesPainter(_flipAnimation.value),
            size: Size.infinite,
          ),
        );
      },
    );
  }

  /// Build card animation (before reveal)
  Widget _buildCardAnimation() {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _shakeAnimation,
        _flipAnimation,
        _bounceAnimation,
      ]),
      builder: (context, child) {
        // Calculate transformations
        final shakeOffset = _shakeAnimation.value * 10 * 
            (0.5 - ((_shakeController.value * 10) % 1)).abs();
        
        final flipAngle = _flipAnimation.value * 3.14159; // 180 degrees
        final bounceScale = 1.0 + _bounceAnimation.value * 0.2;
        
        return Transform.translate(
          offset: Offset(shakeOffset, 0),
          child: Transform.scale(
            scale: bounceScale,
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateY(flipAngle),
              child: Container(
                width: 200,
                height: 280,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.5),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: _flipAnimation.value < 0.5
                    ? _buildCardBack()
                    : _buildCardFront(),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build card back
  Widget _buildCardBack() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue[800]!,
            Colors.purple[800]!,
          ],
        ),
      ),
      child: const Center(
        child: Icon(
          Icons.auto_awesome,
          color: Colors.white,
          size: 60,
        ),
      ),
    );
  }

  /// Build card front (first card preview)
  Widget _buildCardFront() {
    if (widget.result.cards.isEmpty) return Container();
    
    final card = widget.result.cards.first;
    return Transform(
      alignment: Alignment.center,
      transform: Matrix4.identity()..rotateY(3.14159), // Flip back
      child: CardItemWidget(
        card: card,
        showOwnedCount: false,
        width: 200,
        height: 280,
      ),
    );
  }

  /// Build cards reveal
  Widget _buildCardsReveal() {
    if (widget.result.cards.length == 1) {
      return AnimatedBuilder(
        animation: _revealAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _revealAnimation.value,
            child: Opacity(
              opacity: _revealAnimation.value,
              child: CardItemWidget(
                card: widget.result.cards.first,
                showOwnedCount: false,
                width: 250,
                height: 350,
              ),
            ),
          );
        },
      );
    }

    // Multiple cards layout
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Modern title with gradient and glow effect
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient: LinearGradient(
              colors: [
                Colors.purple.withValues(alpha: 0.3),
                Colors.blue.withValues(alpha: 0.3),
              ],
            ),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.purple.withValues(alpha: 0.5),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                '${widget.result.cards.length} CARDS OBTAINED',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w800,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(width: 12),
              Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 28,
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Cards display with better spacing
        Container(
          height: 300, // Increased height to show full cards including glow effects
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            clipBehavior: Clip.none, // Allow glow effects to show outside bounds
            itemCount: widget.result.cards.length,
            itemBuilder: (context, index) {
              final card = widget.result.cards[index];
              final isRevealed = index <= _currentCardIndex;

              return AnimatedBuilder(
                animation: _revealAnimation,
                builder: (context, child) {
                  final animationValue = isRevealed ? 1.0 : 0.0;

                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20), // Increased margin for better spacing
                    child: Transform.scale(
                      scale: 0.8 + (animationValue * 0.2),
                      child: Opacity(
                        opacity: animationValue,
                        child: CardItemWidget(
                          card: card,
                          showOwnedCount: false,
                          width: 140, // Keep reasonable card width
                          height: 210, // Keep reasonable card height
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),

        const SizedBox(height: 24),

        // Modern summary with better styling
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          margin: const EdgeInsets.symmetric(horizontal: 32),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.black.withValues(alpha: 0.4),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Text(
            widget.result.summary.toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.8,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }



  /// Skip animation
  void _skipAnimation() {
    _shakeController.stop();
    _flipController.stop();
    _bounceController.stop();
    _revealController.stop();
    
    setState(() {
      _showCards = true;
      _currentCardIndex = widget.result.cards.length - 1;
    });
    
    _revealController.forward();
  }
}

/// Particles painter for background effects
class _ParticlesPainter extends CustomPainter {
  final double animationValue;

  _ParticlesPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.6 * animationValue)
      ..strokeWidth = 2;

    for (int i = 0; i < 30; i++) {
      final x = (i * 47 + animationValue * 100) % size.width;
      final y = (i * 31 + animationValue * 80) % size.height;
      final radius = 1 + (animationValue * 3);
      
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
